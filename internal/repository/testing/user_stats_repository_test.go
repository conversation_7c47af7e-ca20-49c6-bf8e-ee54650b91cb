package testing

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestUserStatsRepositoriesWithPostgreSQL(t *testing.T) {
	// Use PostgreSQL for this test since it has the triggers
	provider := NewPostgresTestDBProvider()
	defer provider.Cleanup(t)

	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)
	eventRepo := provider.GetEventRepository(t)
	dailyStatsRepo := repository.NewUserDailyStatsRepository(provider.GetDB(t))
	puzzleStatsRepo := repository.NewUserPuzzleStatsRepository(provider.GetDB(t))

	// Create a test user
	user := CreateTestUser(t, userRepo)
	ctx := context.Background()

	t.Run("Test Event Triggers Update Stats", func(t *testing.T) {
		// Create test games and puzzles first
		game := CreateTestGame(t, gameRepo, user.ID)
		puzzle1 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
		puzzle2 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
		puzzle3 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

		// Create puzzle events
		puzzleEvents := []struct {
			puzzleID  string
			solved    bool
			timeSpent int
			eventTime time.Time
		}{
			{puzzle1.ID, true, 30, time.Now().AddDate(0, 0, -2)},  // 2 days ago
			{puzzle2.ID, false, 60, time.Now().AddDate(0, 0, -2)}, // 2 days ago
			{puzzle1.ID, true, 25, time.Now().AddDate(0, 0, -1)},  // 1 day ago
			{puzzle3.ID, true, 45, time.Now()},                    // today
		}

		for _, pe := range puzzleEvents {
			puzzleEventData := models.PuzzleEventData{
				PuzzleID:    pe.puzzleID,
				PuzzleType:  models.PuzzleTypeUser,
				Solved:      pe.solved,
				TimeSpent:   pe.timeSpent,
				MovesPlayed: []string{"e4", "e5", "Nf3"},
			}
			puzzleEventDataJSON, err := json.Marshal(puzzleEventData)
			require.NoError(t, err)

			puzzleEvent := &models.Event{
				ID:        uuid.New().String(),
				UserID:    user.ID,
				EventType: models.EventTypePuzzle,
				EventData: json.RawMessage(puzzleEventDataJSON),
				EventTime: pe.eventTime,
			}
			err = eventRepo.Create(ctx, puzzleEvent)
			require.NoError(t, err)
		}

		// Wait for triggers to execute
		time.Sleep(300 * time.Millisecond)

		// Debug: Check if trigger exists and is enabled
		var triggerInfo struct {
			Exists    bool
			Enabled   bool
			TableName string
		}
		checkErr := provider.GetDB(t).Raw(`
			SELECT 
				EXISTS(SELECT 1 FROM pg_trigger WHERE tgname = 'update_daily_stats_trigger') as exists,
				COALESCE((SELECT tgenabled = 'O' FROM pg_trigger WHERE tgname = 'update_daily_stats_trigger'), false) as enabled,
				COALESCE((SELECT relname FROM pg_class c JOIN pg_trigger t ON c.oid = t.tgrelid WHERE t.tgname = 'update_daily_stats_trigger'), '') as table_name
		`).Scan(&triggerInfo).Error
		require.NoError(t, checkErr)
		t.Logf("Trigger exists: %v, enabled: %v, on table: %s", triggerInfo.Exists, triggerInfo.Enabled, triggerInfo.TableName)

		// Debug: Check if any events were created
		var eventCount int64
		checkErr = provider.GetDB(t).Model(&models.Event{}).Where("user_id = ?", user.ID).Count(&eventCount).Error
		require.NoError(t, checkErr)
		t.Logf("Number of events created: %d", eventCount)

		// Debug: Check if any daily stats exist for the user
		var statsCount int64
		checkErr = provider.GetDB(t).Model(&models.UserDailyStats{}).Where("user_id = ?", user.ID).Count(&statsCount).Error
		require.NoError(t, checkErr)
		t.Logf("Number of daily stats records: %d", statsCount)

		// Debug: Check one of the events to see its structure
		var sampleEvent models.Event
		checkErr = provider.GetDB(t).Model(&models.Event{}).Where("user_id = ?", user.ID).First(&sampleEvent).Error
		require.NoError(t, checkErr)
		t.Logf("Sample event: Type=%s, EventData=%s", sampleEvent.EventType, string(sampleEvent.EventData))

		// Debug: Try to manually call the trigger function to see if it works
		checkErr = provider.GetDB(t).Exec("SELECT update_daily_puzzle_stats($1, $2, $3, $4)",
			user.ID, time.Now().Format("2006-01-02"), true, 30).Error
		if checkErr != nil {
			t.Logf("Error calling trigger function manually: %v", checkErr)
		} else {
			t.Logf("Manual trigger function call succeeded")
		}

		// Check stats count again after manual call
		checkErr = provider.GetDB(t).Model(&models.UserDailyStats{}).Where("user_id = ?", user.ID).Count(&statsCount).Error
		require.NoError(t, checkErr)
		t.Logf("Number of daily stats records after manual call: %d", statsCount)

		// Test daily stats repository
		dailyStats, totalCount, err := dailyStatsRepo.ListByUserID(ctx, user.ID, nil, nil, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(3), totalCount, "Should have 3 days of stats")
		assert.Len(t, dailyStats, 3, "Should return 3 daily stats")

		// Verify stats are sorted by date descending (most recent first)
		assert.True(t, dailyStats[0].Date.After(dailyStats[1].Date), "Stats should be sorted by date descending")

		// Test puzzle stats repository
		puzzleStats, err := puzzleStatsRepo.GetByUserID(ctx, user.ID)
		require.NoError(t, err)
		assert.Len(t, puzzleStats, 3, "Should have stats for 3 different puzzles")

		// Test specific puzzle stats
		puzzle1Stats, err := puzzleStatsRepo.GetByUserIDAndPuzzleID(ctx, user.ID, puzzle1.ID)
		require.NoError(t, err)
		assert.Equal(t, 2, puzzle1Stats.Attempts, "Puzzle-1 should have 2 attempts")
		assert.Equal(t, 2, puzzle1Stats.SuccessCount, "Puzzle-1 should have 2 successes")
		assert.Equal(t, 55, puzzle1Stats.TotalTime, "Puzzle-1 should have 55 total seconds")
	})
}

func TestUserStatsRepositoriesWithFake(t *testing.T) {
	// Use fake repositories for SQLite testing
	provider := NewSQLiteTestDBProvider()
	defer provider.Cleanup(t)

	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)
	eventRepo := provider.GetEventRepository(t)
	dailyStatsRepo := fake.NewUserDailyStatsRepository(provider.GetDB(t))
	puzzleStatsRepo := fake.NewUserPuzzleStatsRepository(provider.GetDB(t))

	// Create a test user
	user := CreateTestUser(t, userRepo)
	ctx := context.Background()

	t.Run("Test Fake Repositories Calculate Stats from Events", func(t *testing.T) {
		// Create test games and puzzles first
		game := CreateTestGame(t, gameRepo, user.ID)
		puzzle1 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
		puzzle2 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
		puzzle3 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

		// Create puzzle events
		puzzleEvents := []struct {
			puzzleID  string
			solved    bool
			timeSpent int
			eventTime time.Time
		}{
			{puzzle1.ID, true, 30, time.Now().AddDate(0, 0, -2)},  // 2 days ago
			{puzzle2.ID, false, 60, time.Now().AddDate(0, 0, -2)}, // 2 days ago
			{puzzle1.ID, true, 25, time.Now().AddDate(0, 0, -1)},  // 1 day ago
			{puzzle3.ID, true, 45, time.Now()},                    // today
		}

		for _, pe := range puzzleEvents {
			puzzleEventData := models.PuzzleEventData{
				PuzzleID:    pe.puzzleID,
				PuzzleType:  models.PuzzleTypeUser,
				Solved:      pe.solved,
				TimeSpent:   pe.timeSpent,
				MovesPlayed: []string{"e4", "e5", "Nf3"},
			}
			puzzleEventDataJSON, err := json.Marshal(puzzleEventData)
			require.NoError(t, err)

			puzzleEvent := &models.Event{
				ID:        uuid.New().String(),
				UserID:    user.ID,
				EventType: models.EventTypePuzzle,
				EventData: json.RawMessage(puzzleEventDataJSON),
				EventTime: pe.eventTime,
			}
			err = eventRepo.Create(ctx, puzzleEvent)
			require.NoError(t, err)
		}

		// Test daily stats repository (fake implementation)
		dailyStats, totalCount, err := dailyStatsRepo.ListByUserID(ctx, user.ID, nil, nil, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(3), totalCount, "Should have 3 days of stats")
		assert.Len(t, dailyStats, 3, "Should return 3 daily stats")

		// Find today's stats
		var todayStats *models.UserDailyStats
		today := time.Now().Format("2006-01-02")
		for _, stats := range dailyStats {
			if stats.Date.Format("2006-01-02") == today {
				todayStats = &stats
				break
			}
		}
		require.NotNil(t, todayStats, "Should have stats for today")
		assert.Equal(t, 1, todayStats.PuzzleSuccess, "Today should have 1 successful puzzle")
		assert.Equal(t, 1, todayStats.PuzzleTotal, "Today should have 1 total puzzle")
		assert.Equal(t, 45, todayStats.PuzzleTotalDuration, "Today should have 45 seconds total duration")

		// Test puzzle stats repository (fake implementation)
		puzzleStats, err := puzzleStatsRepo.GetByUserID(ctx, user.ID)
		require.NoError(t, err)
		assert.Len(t, puzzleStats, 3, "Should have stats for 3 different puzzles")

		// Test specific puzzle stats
		puzzle1Stats, err := puzzleStatsRepo.GetByUserIDAndPuzzleID(ctx, user.ID, puzzle1.ID)
		require.NoError(t, err)
		assert.Equal(t, 2, puzzle1Stats.Attempts, "Puzzle-1 should have 2 attempts")
		assert.Equal(t, 2, puzzle1Stats.SuccessCount, "Puzzle-1 should have 2 successes")
		assert.Equal(t, 55, puzzle1Stats.TotalTime, "Puzzle-1 should have 55 total seconds")
		assert.Equal(t, 27.5, puzzle1Stats.AverageTime, "Puzzle-1 should have 27.5 seconds average time")
		assert.True(t, puzzle1Stats.LastAttemptSuccess, "Last attempt should be successful")

		// Test non-existent puzzle
		_, err = puzzleStatsRepo.GetByUserIDAndPuzzleID(ctx, user.ID, "non-existent")
		assert.Error(t, err, "Should return error for non-existent puzzle")
	})

	t.Run("Test Date Range Filtering", func(t *testing.T) {
		// Create a separate user for this test to avoid interference
		user2 := CreateTestUser(t, userRepo)

		// Create test game and puzzle for this user
		game2 := CreateTestGame(t, gameRepo, user2.ID)
		testPuzzle := CreateTestPuzzle(t, puzzleRepo, game2.ID, user2.ID)

		// Create a single event for yesterday
		yesterday := time.Now().AddDate(0, 0, -1)
		puzzleEventData := models.PuzzleEventData{
			PuzzleID:    testPuzzle.ID,
			PuzzleType:  models.PuzzleTypeUser,
			Solved:      true,
			TimeSpent:   30,
			MovesPlayed: []string{"e4", "e5", "Nf3"},
		}
		puzzleEventDataJSON, err := json.Marshal(puzzleEventData)
		require.NoError(t, err)

		puzzleEvent := &models.Event{
			ID:        uuid.New().String(),
			UserID:    user2.ID,
			EventType: models.EventTypePuzzle,
			EventData: json.RawMessage(puzzleEventDataJSON),
			EventTime: yesterday,
		}
		err = eventRepo.Create(ctx, puzzleEvent)
		require.NoError(t, err)

		// Test filtering by date range
		dailyStats, totalCount, err := dailyStatsRepo.ListByUserID(ctx, user2.ID, &yesterday, &yesterday, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(1), totalCount, "Should have 1 day of stats for yesterday")
		assert.Len(t, dailyStats, 1, "Should return 1 daily stat for yesterday")

		if len(dailyStats) > 0 {
			assert.Equal(t, 1, dailyStats[0].PuzzleSuccess, "Yesterday should have 1 successful puzzle")
			assert.Equal(t, 1, dailyStats[0].PuzzleTotal, "Yesterday should have 1 total puzzle")
		}
	})
}
