-- 004-daily-quest-requirements.sql
-- This migration adds support for daily quest requirements and updates user daily stats
-- to include sprint and arrow duel tracking with quest completion functionality.

-- Insert initial quest requirements
-- This will run after GORM auto-migration creates the daily_quest_requirements table
INSERT INTO daily_quest_requirements (id, type, name, description, target, is_active, created_at, updated_at)
VALUES 
    ('quest-sprint-daily', 'sprint', 'Daily Sprint', 'Complete at least one puzzle sprint', 1, true, NOW(), NOW()),
    ('quest-arrow-duel-daily', 'arrow_duel', 'Daily Arrow Duel', 'Participate in at least one arrow duel', 1, true, NOW(), NOW())
ON CONFLICT (type) DO NOTHING;

-- Update the existing trigger function to handle sprint and arrow duel stats
CREATE OR REPLACE FUNCTION trigger_update_daily_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle puzzle events (existing logic)
    IF NEW.event_type = 'puzzle' AND NEW.event_sub_type = 'end' THEN
        -- Check if this is an arrow duel
        IF NEW.event_data::jsonb ? 'attemptType' AND 
           NEW.event_data::jsonb->>'attemptType' = 'arrow_duel' THEN
            CALL update_arrow_duel_daily_stats(NEW.user_id, NEW.created_at::date, NEW.event_data::jsonb);
        ELSE
            -- Regular puzzle logic (existing)
            CALL update_puzzle_daily_stats(NEW.user_id, NEW.created_at::date, NEW.event_data::jsonb);
        END IF;
    END IF;
    
    -- Handle sprint events (NEW)
    IF NEW.event_type = 'puzzle_sprint' AND NEW.event_sub_type = 'end' THEN
        CALL update_sprint_daily_stats(NEW.user_id, NEW.created_at::date, NEW.event_data::jsonb);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create procedure to update sprint daily stats
CREATE OR REPLACE PROCEDURE update_sprint_daily_stats(
    p_user_id VARCHAR(36),
    p_date DATE,
    p_event_data JSONB
)
LANGUAGE plpgsql AS $$
DECLARE
    v_status TEXT;
    v_duration INT;
    v_success_increment INT := 0;
BEGIN
    -- Extract sprint data
    v_status := p_event_data->>'status';
    v_duration := COALESCE((p_event_data->>'duration')::INT, 0);
    
    -- Determine if sprint was successful
    IF v_status = 'completed_success' THEN
        v_success_increment := 1;
    END IF;
    
    -- Insert or update daily stats
    INSERT INTO user_daily_stats (
        id, user_id, date, sprint_success, sprint_total, sprint_total_duration,
        created_at, updated_at
    ) VALUES (
        gen_random_uuid()::text, p_user_id, p_date, v_success_increment, 1, v_duration,
        NOW(), NOW()
    )
    ON CONFLICT (user_id, date) DO UPDATE SET
        sprint_success = user_daily_stats.sprint_success + v_success_increment,
        sprint_total = user_daily_stats.sprint_total + 1,
        sprint_total_duration = user_daily_stats.sprint_total_duration + v_duration,
        updated_at = NOW();
    
    -- Update quest completion status
    CALL update_quest_completion(p_user_id, p_date);
END;
$$;

-- Create procedure to update arrow duel daily stats
CREATE OR REPLACE PROCEDURE update_arrow_duel_daily_stats(
    p_user_id VARCHAR(36),
    p_date DATE,
    p_event_data JSONB
)
LANGUAGE plpgsql AS $$
DECLARE
    v_was_correct BOOLEAN;
    v_time_taken INT;
    v_success_increment INT := 0;
BEGIN
    -- Extract arrow duel data
    v_was_correct := COALESCE((p_event_data->>'was_correct')::BOOLEAN, false);
    v_time_taken := COALESCE((p_event_data->>'time_taken_ms')::INT, 0);
    
    -- Determine if arrow duel was successful
    IF v_was_correct THEN
        v_success_increment := 1;
    END IF;
    
    -- Insert or update daily stats
    INSERT INTO user_daily_stats (
        id, user_id, date, arrow_duel_success, arrow_duel_total, arrow_duel_total_duration,
        created_at, updated_at
    ) VALUES (
        gen_random_uuid()::text, p_user_id, p_date, v_success_increment, 1, v_time_taken,
        NOW(), NOW()
    )
    ON CONFLICT (user_id, date) DO UPDATE SET
        arrow_duel_success = user_daily_stats.arrow_duel_success + v_success_increment,
        arrow_duel_total = user_daily_stats.arrow_duel_total + 1,
        arrow_duel_total_duration = user_daily_stats.arrow_duel_total_duration + v_time_taken,
        updated_at = NOW();
    
    -- Update quest completion status
    CALL update_quest_completion(p_user_id, p_date);
END;
$$;

-- Create procedure to check and update quest completion
CREATE OR REPLACE PROCEDURE update_quest_completion(
    p_user_id VARCHAR(36),
    p_date DATE
)
LANGUAGE plpgsql AS $$
DECLARE
    v_stats_record RECORD;
    v_quest_completed BOOLEAN := true;
    v_requirement RECORD;
    v_previous_streak INT := 0;
    v_new_streak INT;
BEGIN
    -- Get current daily stats
    SELECT * INTO v_stats_record
    FROM user_daily_stats
    WHERE user_id = p_user_id AND date = p_date;
    
    IF v_stats_record IS NULL THEN
        RETURN; -- No stats record exists yet
    END IF;
    
    -- Check each active quest requirement
    FOR v_requirement IN 
        SELECT type, target FROM daily_quest_requirements WHERE is_active = true
    LOOP
        CASE v_requirement.type
            WHEN 'sprint' THEN
                IF COALESCE(v_stats_record.sprint_total, 0) < v_requirement.target THEN
                    v_quest_completed := false;
                    EXIT; -- No need to check further
                END IF;
            WHEN 'arrow_duel' THEN
                IF COALESCE(v_stats_record.arrow_duel_total, 0) < v_requirement.target THEN
                    v_quest_completed := false;
                    EXIT;
                END IF;
        END CASE;
    END LOOP;
    
    -- Calculate quest streak
    IF v_quest_completed THEN
        -- Get previous day's quest streak
        SELECT COALESCE(quest_streak, 0) INTO v_previous_streak
        FROM user_daily_stats
        WHERE user_id = p_user_id 
          AND date = p_date - INTERVAL '1 day'
          AND quest_completed = true;
        
        v_new_streak := COALESCE(v_previous_streak, 0) + 1;
    ELSE
        v_new_streak := 0;
    END IF;
    
    -- Update quest completion status
    UPDATE user_daily_stats 
    SET 
        quest_completed = v_quest_completed,
        quest_streak = v_new_streak,
        updated_at = NOW()
    WHERE user_id = p_user_id AND date = p_date;
END;
$$;

-- Update the existing update_puzzle_daily_stats procedure to also update quest completion
CREATE OR REPLACE PROCEDURE update_puzzle_daily_stats(
    p_user_id VARCHAR(36),
    p_date DATE,
    p_event_data JSONB
)
LANGUAGE plpgsql AS $$
DECLARE
    v_was_correct BOOLEAN;
    v_time_taken INT;
    v_success_increment INT := 0;
    v_current_streak INT;
    v_new_streak INT;
BEGIN
    -- Extract puzzle data
    v_was_correct := COALESCE((p_event_data->>'was_correct')::BOOLEAN, false);
    v_time_taken := COALESCE((p_event_data->>'time_taken_ms')::INT, 0);
    
    -- Determine if puzzle was successful
    IF v_was_correct THEN
        v_success_increment := 1;
    END IF;
    
    -- Get current streak from the latest daily stats
    SELECT COALESCE(streak, 0) INTO v_current_streak
    FROM user_daily_stats
    WHERE user_id = p_user_id
    ORDER BY date DESC
    LIMIT 1;
    
    -- Calculate new streak (existing logic)
    IF v_was_correct THEN
        -- Check if user had activity yesterday
        IF EXISTS (
            SELECT 1 FROM user_daily_stats
            WHERE user_id = p_user_id 
              AND date = p_date - INTERVAL '1 day'
              AND puzzle_total > 0
        ) THEN
            v_new_streak := v_current_streak + 1;
        ELSE
            v_new_streak := 1; -- Start new streak
        END IF;
    ELSE
        v_new_streak := 0; -- Reset streak on failure
    END IF;
    
    -- Insert or update daily stats
    INSERT INTO user_daily_stats (
        id, user_id, date, puzzle_success, puzzle_total, puzzle_total_duration, streak,
        created_at, updated_at
    ) VALUES (
        gen_random_uuid()::text, p_user_id, p_date, v_success_increment, 1, v_time_taken, v_new_streak,
        NOW(), NOW()
    )
    ON CONFLICT (user_id, date) DO UPDATE SET
        puzzle_success = user_daily_stats.puzzle_success + v_success_increment,
        puzzle_total = user_daily_stats.puzzle_total + 1,
        puzzle_total_duration = user_daily_stats.puzzle_total_duration + v_time_taken,
        streak = v_new_streak,
        updated_at = NOW();
    
    -- Update quest completion status
    CALL update_quest_completion(p_user_id, p_date);
END;
$$;