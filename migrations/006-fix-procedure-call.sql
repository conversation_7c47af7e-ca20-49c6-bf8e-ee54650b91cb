-- Fix procedure call name

-- Update the update_puzzle_daily_stats procedure to call the correct procedure name
CREATE OR REPLACE PROCEDURE update_puzzle_daily_stats(
    p_user_id VARCHAR(36),
    p_date DATE,
    p_event_data JSONB
)
LANGUAGE plpgsql AS $$
DECLARE
    v_was_correct BOOLEAN;
    v_time_taken INT;
    v_success_increment INT := 0;
    v_current_streak INT;
    v_new_streak INT;
BEGIN
    -- Extract puzzle data - handle both old and new field names
    -- Try new field names first, then fall back to old ones
    IF p_event_data ? 'was_correct' THEN
        v_was_correct := COALESCE((p_event_data->>'was_correct')::BOOLEAN, false);
    ELSE
        v_was_correct := COALESCE((p_event_data->>'solved')::BOOLEAN, false);
    END IF;
    
    IF p_event_data ? 'time_taken_ms' THEN
        v_time_taken := COALESCE((p_event_data->>'time_taken_ms')::INT, 0);
    ELSE
        -- Convert seconds to milliseconds for consistency
        v_time_taken := COALESCE((p_event_data->>'time_spent')::INT, 0) * 1000;
    END IF;
    
    -- Determine if puzzle was successful
    IF v_was_correct THEN
        v_success_increment := 1;
    END IF;
    
    -- Get current streak from the latest daily stats
    SELECT COALESCE(streak, 0) INTO v_current_streak
    FROM user_daily_stats
    WHERE user_id = p_user_id
    ORDER BY date DESC
    LIMIT 1;
    
    -- Calculate new streak (existing logic)
    IF v_was_correct THEN
        -- Check if user had activity yesterday
        IF EXISTS (
            SELECT 1 FROM user_daily_stats
            WHERE user_id = p_user_id 
              AND date = p_date - INTERVAL '1 day'
              AND puzzle_total > 0
        ) THEN
            v_new_streak := v_current_streak + 1;
        ELSE
            v_new_streak := 1;
        END IF;
    ELSE
        v_new_streak := 0;
    END IF;
    
    -- Insert or update daily stats
    INSERT INTO user_daily_stats (
        id, user_id, date, puzzle_success, puzzle_total, puzzle_total_duration, streak,
        created_at, updated_at
    ) VALUES (
        gen_random_uuid()::text, p_user_id, p_date, v_success_increment, 1, v_time_taken, v_new_streak,
        NOW(), NOW()
    )
    ON CONFLICT (user_id, date) DO UPDATE SET
        puzzle_success = user_daily_stats.puzzle_success + v_success_increment,
        puzzle_total = user_daily_stats.puzzle_total + 1,
        puzzle_total_duration = user_daily_stats.puzzle_total_duration + v_time_taken,
        streak = v_new_streak,
        updated_at = NOW();

    -- Also create/update individual puzzle stats
    -- Extract puzzle ID and other fields
    DECLARE
        v_puzzle_id VARCHAR(255);
        v_puzzle_type VARCHAR(20);
        v_is_disliked BOOLEAN := NULL;
    BEGIN
        v_puzzle_id := p_event_data->>'puzzle_id';
        v_puzzle_type := COALESCE(p_event_data->>'puzzle_type', 'user');
        
        -- Parse is_disliked field if present
        IF p_event_data ? 'is_disliked' AND p_event_data->>'is_disliked' IS NOT NULL THEN
            v_is_disliked := (p_event_data->>'is_disliked')::boolean;
        END IF;
        
        -- Only create puzzle stats for user-generated puzzles (not lichess puzzles)
        IF v_puzzle_type = 'user' AND v_puzzle_id IS NOT NULL THEN
            -- Insert or update puzzle stats
            INSERT INTO user_puzzle_stats (
                id, user_id, puzzle_id, attempts, success_count, total_time, average_time,
                last_attempt_time, last_attempt_success, is_disliked, disliked_at,
                created_at, updated_at
            ) VALUES (
                gen_random_uuid()::text, p_user_id, v_puzzle_id, 1, 
                CASE WHEN v_was_correct THEN 1 ELSE 0 END,
                v_time_taken / 1000, -- Convert back to seconds for storage
                (v_time_taken / 1000)::float,
                NOW(), v_was_correct,
                COALESCE(v_is_disliked, false),
                CASE WHEN v_is_disliked = true THEN NOW() ELSE NULL END,
                NOW(), NOW()
            )
            ON CONFLICT (user_id, puzzle_id) DO UPDATE SET
                attempts = user_puzzle_stats.attempts + 1,
                success_count = user_puzzle_stats.success_count + CASE WHEN v_was_correct THEN 1 ELSE 0 END,
                total_time = user_puzzle_stats.total_time + (v_time_taken / 1000),
                average_time = (user_puzzle_stats.total_time + (v_time_taken / 1000))::float / (user_puzzle_stats.attempts + 1),
                last_attempt_time = NOW(),
                last_attempt_success = v_was_correct,
                is_disliked = COALESCE(v_is_disliked, user_puzzle_stats.is_disliked),
                disliked_at = CASE 
                    WHEN v_is_disliked = true AND user_puzzle_stats.is_disliked = false THEN NOW()
                    WHEN v_is_disliked = false THEN NULL
                    ELSE user_puzzle_stats.disliked_at
                END,
                updated_at = NOW();
        END IF;
    END;

    -- Check and update quest completion status
    CALL update_quest_completion(p_user_id, p_date);
END;
$$;
