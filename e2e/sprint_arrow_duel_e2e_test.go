package e2e

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/api"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestArrowDuelSprintAPI_E2E(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	// Generate an admin JWT token for setup
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Create a test user and get auth token
	userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	userPassword := "ArrowDuelSprintTestPassword123!"
	userID := createUser(t, adminToken, userEmail, userPassword)
	require.NotEmpty(t, userID, "User ID should not be empty")

	// Generate user token
	userToken := generateUserToken(t, userID, userEmail)
	require.NotEmpty(t, userToken, "User token should not be empty")

	var sessionID string
	var firstPuzzle *api.PuzzleForSprint
	var secondPuzzle *api.PuzzleForSprint

	// Test 1: Start an arrow-duel sprint
	t.Run("StartArrowDuelSprint", func(t *testing.T) {
		startReq := api.StartSprintRequest{
			EloType: "arrowduel 5/30", // Arrow-duel sprint type
		}

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/start", headers, startReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusCreated, resp.StatusCode)

		var startResp api.StartSprintResponse
		err := json.NewDecoder(resp.Body).Decode(&startResp)
		require.NoError(t, err)

		sessionID = startResp.SessionID
		assert.NotEmpty(t, sessionID, "Session ID should not be empty")
		// Arrow-duel sprint should return arrow_duel attempt type
		assert.Equal(t, "arrow_duel", startResp.AttemptType) // Should return arrow_duel for arrow-duel sprints

		t.Logf("✅ Started arrow-duel sprint with session ID: %s", sessionID)
	})

	// Test 2: Get first batch of puzzles
	t.Run("GetNextPuzzles", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		nextReq := api.NextPuzzlesRequest{
			Count: 5,
		}

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/"+sessionID+"/next-puzzles", headers, nextReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var nextResp api.NextPuzzlesResponse
		err := json.NewDecoder(resp.Body).Decode(&nextResp)
		require.NoError(t, err)

		assert.GreaterOrEqual(t, len(nextResp.Puzzles), 2, "Should get at least 2 puzzles")
		firstPuzzle = &nextResp.Puzzles[0]
		if len(nextResp.Puzzles) > 1 {
			secondPuzzle = &nextResp.Puzzles[1]
		}

		// Verify puzzle structure for arrow-duel
		assert.NotEmpty(t, firstPuzzle.PuzzleID, "Puzzle ID should not be empty")
		assert.Equal(t, "arrow_duel", firstPuzzle.AttemptType, "Should be arrow_duel type")
		assert.NotEmpty(t, firstPuzzle.SolutionMoves, "Solution moves should not be empty")

		t.Logf("✅ Got %d puzzles for arrow-duel sprint", len(nextResp.Puzzles))
	})

	// Test 3: Submit arrow-duel results with mixed correct/incorrect attempts
	t.Run("SubmitArrowDuelResults", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")
		require.NotNil(t, firstPuzzle, "First puzzle should be set from previous test")
		require.NotNil(t, secondPuzzle, "Second puzzle should be set from previous test")

		// Simulate arrow-duel attempts with candidate moves
		submitReq := api.SubmitResultsRequest{
			Results: []api.PuzzleAttemptRequest{
				{
					// Correct arrow-duel attempt
					PuzzleID:         firstPuzzle.PuzzleID,
					SequenceInSprint: firstPuzzle.SequenceInSprint,
					UserMoves:        firstPuzzle.SolutionMoves, // Submit correct solution
					WasCorrect:       true,
					TimeTakenMs:      4500, // 4.5 seconds
					AttemptedAt:      time.Now(),
					AttemptType:      "arrow_duel",
					CandidateMoves:   []string{"e2e3", firstPuzzle.SolutionMoves[0]}, // [blunder, correct]
					ChosenMove:       &firstPuzzle.SolutionMoves[0],                  // Chose correct move
				},
				{
					// Incorrect arrow-duel attempt (chose blunder)
					PuzzleID:         secondPuzzle.PuzzleID,
					SequenceInSprint: secondPuzzle.SequenceInSprint,
					UserMoves:        []string{"Qh4"}, // Submit incorrect move
					WasCorrect:       false,
					TimeTakenMs:      8200, // 8.2 seconds
					AttemptedAt:      time.Now(),
					AttemptType:      "arrow_duel",
					CandidateMoves:   []string{"Qh4", "Qh5"},    // [blunder, correct]
					ChosenMove:       stringPtrArrowDuel("Qh4"), // Chose blunder move
				},
			},
		}

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/"+sessionID+"/results", headers, submitReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var submitResp api.SubmitResultsResponse
		err := json.NewDecoder(resp.Body).Decode(&submitResp)
		require.NoError(t, err)

		assert.Equal(t, 2, submitResp.ProcessedCount, "Should process 2 results")
		assert.Equal(t, models.SprintStatusActive, submitResp.SessionStatus)
		assert.Equal(t, 1, submitResp.MistakesCount, "Should have 1 mistake from incorrect attempt")

		t.Logf("✅ Submitted 2 arrow-duel attempts: 1 correct, 1 incorrect")
	})

	// Test 4: End sprint with client-side stats
	t.Run("EndSprintWithClientStats", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		// End sprint with client-provided final stats
		endReq := api.EndSprintRequest{
			PuzzlesSolved: intPtrArrowDuel(1), // Client says 1 puzzle solved
			MistakesMade:  intPtrArrowDuel(1), // Client says 1 mistake made
		}

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/"+sessionID+"/end", headers, endReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var endResp api.EndSprintResponse
		err := json.NewDecoder(resp.Body).Decode(&endResp)
		require.NoError(t, err)

		assert.Equal(t, sessionID, endResp.SessionID)
		assert.Contains(t, []models.SprintStatus{
			models.SprintStatusCompletedSuccess,
			models.SprintStatusCompletedFailMistakes,
			models.SprintStatusCompletedFailTime,
		}, endResp.Status)

		// Verify client stats were used (client has final say on results)
		assert.Equal(t, 1, endResp.PuzzlesSolved, "Should use client-provided puzzles solved")
		assert.Equal(t, 1, endResp.MistakesMade, "Should use client-provided mistakes made")

		// ELO should change for arrow-duel sprints
		require.NotNil(t, endResp.EloChange, "ELO change should not be nil for arrow-duel")
		// ELO change should be for arrow-duel type sprint

		t.Logf("✅ Ended sprint with status: %s, ELO change: %+d", endResp.Status, endResp.EloChange.RatingChange)
	})

	// Test 5: Retrieve sprint puzzles and verify arrow-duel data
	t.Run("GetSprintPuzzlesWithArrowDuelData", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		// Get all attempted puzzles
		resp := makeRequest(t, "GET", "/api/v1/users/me/sprint/"+sessionID+"/puzzles?status=attempted", headers, nil)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var puzzlesResp api.SprintPuzzlesResponse
		err := json.NewDecoder(resp.Body).Decode(&puzzlesResp)
		require.NoError(t, err)

		// We expect at least 2 puzzles (the ones we just submitted)
		// There might be more if the test environment has leftover data
		assert.GreaterOrEqual(t, len(puzzlesResp.Puzzles), 2, "Should have at least 2 attempted puzzles")
		assert.GreaterOrEqual(t, puzzlesResp.TotalCount, int64(2), "Total count should be at least 2")

		// Find the correct and incorrect attempts
		var correctAttempt, incorrectAttempt *api.SprintPuzzleResponse
		for i := range puzzlesResp.Puzzles {
			puzzle := &puzzlesResp.Puzzles[i]
			if puzzle.WasCorrect != nil && *puzzle.WasCorrect {
				correctAttempt = puzzle
			} else if puzzle.WasCorrect != nil && !*puzzle.WasCorrect {
				incorrectAttempt = puzzle
			}
		}

		require.NotNil(t, correctAttempt, "Should find correct attempt")
		require.NotNil(t, incorrectAttempt, "Should find incorrect attempt")

		// Verify correct attempt arrow-duel data
		assert.Equal(t, "solved", correctAttempt.AttemptStatus)
		require.NotNil(t, correctAttempt.AttemptType, "Attempt type should not be nil")
		assert.Equal(t, "arrow_duel", *correctAttempt.AttemptType)
		assert.Len(t, correctAttempt.CandidateMoves, 2, "Should have 2 candidate moves")
		require.NotNil(t, correctAttempt.ChosenMove, "Chosen move should not be nil")
		assert.Equal(t, correctAttempt.CandidateMoves[1], *correctAttempt.ChosenMove, "Should have chosen the correct move (second candidate)")

		// Verify incorrect attempt arrow-duel data
		assert.Equal(t, "failed", incorrectAttempt.AttemptStatus)
		require.NotNil(t, incorrectAttempt.AttemptType, "Attempt type should not be nil")
		assert.Equal(t, "arrow_duel", *incorrectAttempt.AttemptType)
		assert.Equal(t, []string{"Qh4", "Qh5"}, incorrectAttempt.CandidateMoves, "Should have expected candidate moves")
		require.NotNil(t, incorrectAttempt.ChosenMove, "Chosen move should not be nil")
		assert.Equal(t, "Qh4", *incorrectAttempt.ChosenMove, "Should have chosen the blunder move")

		t.Logf("✅ Verified arrow-duel data retrieval:")
		t.Logf("   Correct attempt: chose %s from %v", *correctAttempt.ChosenMove, correctAttempt.CandidateMoves)
		t.Logf("   Incorrect attempt: chose %s from %v", *incorrectAttempt.ChosenMove, incorrectAttempt.CandidateMoves)
	})

	// Test 6: Filter arrow-duel attempts specifically
	t.Run("FilterArrowDuelAttempts", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		// Test filtering by arrow_duel attempt type
		resp := makeRequest(t, "GET", "/api/v1/users/me/sprint/"+sessionID+"/puzzles?attempt_type=arrow_duel", headers, nil)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var puzzlesResp api.SprintPuzzlesResponse
		err := json.NewDecoder(resp.Body).Decode(&puzzlesResp)
		require.NoError(t, err)

		assert.GreaterOrEqual(t, len(puzzlesResp.Puzzles), 2, "Should have at least 2 arrow-duel attempts")

		// Verify all returned puzzles are arrow-duel type
		for _, puzzle := range puzzlesResp.Puzzles {
			require.NotNil(t, puzzle.AttemptType, "Attempt type should not be nil")
			assert.Equal(t, "arrow_duel", *puzzle.AttemptType, "All returned puzzles should be arrow_duel type")
			assert.Len(t, puzzle.CandidateMoves, 2, "All arrow-duel puzzles should have 2 candidate moves")
			require.NotNil(t, puzzle.ChosenMove, "All arrow-duel puzzles should have chosen move")
		}

		t.Logf("✅ Filtering by arrow_duel type returned %d puzzles", len(puzzlesResp.Puzzles))
	})

	// Test 7: Filter failed arrow-duel attempts for mistake review
	t.Run("FilterFailedArrowDuelForReview", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		// Test filtering by failed arrow_duel attempts (for mistake review)
		resp := makeRequest(t, "GET", "/api/v1/users/me/sprint/"+sessionID+"/puzzles?status=failed&attempt_type=arrow_duel", headers, nil)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var puzzlesResp api.SprintPuzzlesResponse
		err := json.NewDecoder(resp.Body).Decode(&puzzlesResp)
		require.NoError(t, err)

		assert.Equal(t, 1, len(puzzlesResp.Puzzles), "Should have 1 failed arrow-duel attempt")
		require.True(t, len(puzzlesResp.Puzzles) > 0, "Expected at least 1 puzzle in response")

		failedPuzzle := puzzlesResp.Puzzles[0]
		assert.Equal(t, "failed", failedPuzzle.AttemptStatus)
		require.NotNil(t, failedPuzzle.AttemptType, "Attempt type should not be nil")
		assert.Equal(t, "arrow_duel", *failedPuzzle.AttemptType)

		// This is the data the client needs for mistake review
		assert.Equal(t, []string{"Qh4", "Qh5"}, failedPuzzle.CandidateMoves, "Should have candidate moves for review")
		require.NotNil(t, failedPuzzle.ChosenMove, "Should have chosen move for review")
		assert.Equal(t, "Qh4", *failedPuzzle.ChosenMove, "Should show the blunder move was chosen")
		assert.Equal(t, []string{"Qh4"}, failedPuzzle.UserMoves, "Should show user's actual moves")

		t.Logf("✅ Mistake review data available:")
		t.Logf("   User chose: %s (blunder)", *failedPuzzle.ChosenMove)
		t.Logf("   Correct choice was: %s", failedPuzzle.CandidateMoves[1])
		t.Logf("   User moves: %v", failedPuzzle.UserMoves)
		t.Logf("   Solution moves: %v", failedPuzzle.SolutionMoves)
	})
}

// Helper functions specific to arrow duel test
func stringPtrArrowDuel(s string) *string {
	return &s
}

func intPtrArrowDuel(i int) *int {
	return &i
}
